# Python
__pycache__/
/__pycache__/*
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
env/*
.env/*
venv/
ENV/
env.bak/
venv.bak/
.venv/

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env
*.env.example
*.env.staging
.env*.local
!.env.example

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/
gmx_creator.log
debug.log
error.log

# Credentials and Sensitive Data
credentials/
!requirements.txt
!requirements_.txt
!README.txt
api_keys.json
config.json
secrets.json

# Browser and Automation
driver/
drivers/
*.exe
chromedriver*
geckodriver*
msedgedriver*
downloaded_files/
temp_profile/
chrome_profile/
firefox_profile/
user_data/

# Extensions and Add-ons
extensions/
*.crx
*.xpi
*.pem

# Temporary Files
temp/
tmp/
*.tmp
*.temp
.cache/
cache/

# Lock Files
*.lock
.lock

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
gmx_creator.log
# Project Specific
./credentials/*
credentials/gmx_accounts.txt
credentials/used_phone_numbers.txt
credentials/phone_numbers.txt
credentials/created_accounts.txt
backup_*.py
*_backup.py

# Captcha and SMS Services
captcha_images/
sms_codes/
verification_codes/

# Screenshots and Media
screenshots/
*.png
*.jpg
*.jpeg
*.gif
*.bmp
!logo.png
!icon.png

# Database Files
*.db
*.sqlite
*.sqlite3

# Jupyter Notebooks
.ipynb_checkpoints/
*.ipynb

# pytest
.pytest_cache/
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Project-specific files to ignore
extensions/shared_proxy_extension/background.js
proxy_usage.json
successful_user_agents.json